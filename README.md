# Portfolio Project

A professional portfolio website built with <PERSON>act (frontend) and Express.js (backend).

## Project Structure

```
Portfolio/
├── client/          # React frontend application
│   ├── src/         # Source code
│   ├── public/      # Static assets
│   ├── package.json # Frontend dependencies
│   └── ...
└── server/          # Express.js backend application
    ├── models/      # Database models
    ├── routes/      # API routes
    ├── uploads/     # File uploads
    ├── server.js    # Main server file
    ├── package.json # Backend dependencies
    └── ...
```

## Prerequisites

- Node.js (v16 or higher)
- npm (comes with Node.js)
- MongoDB Atlas account (for database)

## Installation & Setup

### 1. Install Dependencies

**Install Frontend Dependencies:**
```bash
cd client
npm install
```

**Install Backend Dependencies:**
```bash
cd server
npm install
```

### 2. Environment Configuration

Create a `.env` file in the `server` directory:
```bash
cd server
touch .env
```

Add your MongoDB connection string to `.env`:
```
MONGODB_URI=your_mongodb_atlas_connection_string
PORT=5001
```

### 3. Running the Project

**Option 1: Run Both Services Separately**

Terminal 1 (Backend):
```bash
cd server
npm run dev
```

Terminal 2 (Frontend):
```bash
cd client
npm run dev
```

**Option 2: Run with Concurrently (Recommended)**

From the root directory, you can create a simple script to run both:

Create `start.sh`:
```bash
#!/bin/bash
echo "Starting Portfolio Project..."
echo "Starting backend server..."
cd server && npm run dev &
echo "Starting frontend client..."
cd client && npm run dev &
wait
```

Make it executable and run:
```bash
chmod +x start.sh
./start.sh
```

## Access the Application

- **Frontend**: http://localhost:5174 (or the port shown in terminal)
- **Backend API**: http://localhost:5001
- **Contact API**: http://localhost:5001/api/contact

## Features

- ✅ Responsive portfolio website
- ✅ Smooth animations and text effects
- ✅ Contact form with MongoDB integration
- ✅ Portfolio image galleries
- ✅ Professional navigation and UX
- ✅ Mobile-friendly design

## Development

### Frontend (React + Vite)
- Hot reload enabled
- ESLint configured
- Tailwind CSS for styling
- Framer Motion for animations
- GSAP for advanced animations

### Backend (Express.js)
- MongoDB integration
- Contact form API
- File upload support
- CORS enabled
- Environment variables

## Production Deployment

### Frontend Build
```bash
cd client
npm run build
```

### Backend Production
```bash
cd server
npm start
```

## Troubleshooting

### Common Issues

1. **Port already in use**
   - Change the port in server/.env or client/vite.config.js

2. **MongoDB connection failed**
   - Check your MONGODB_URI in server/.env
   - Ensure your IP is whitelisted in MongoDB Atlas

3. **Dependencies not found**
   - Run `npm install` in both client and server directories

4. **Build errors**
   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`

## Scripts

### Client Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Server Scripts
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server

## Support

If you encounter any issues, check:
1. Node.js and npm versions
2. MongoDB Atlas connection
3. Environment variables
4. Port availability

---

**Made with ❤️ for professional portfolio showcase**
